<svg width="98" height="68" viewBox="0 0 98 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_14540_49838)">
<rect width="98" height="68" rx="4" fill="#F3F3F3"/>
<mask id="mask0_14540_49838" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="98" height="68">
<rect x="0.5" y="0.5" width="97" height="67" rx="3.5" fill="#F3F3F3" stroke="#CCCCCC"/>
</mask>
<g mask="url(#mask0_14540_49838)">
<g filter="url(#filter0_n_14540_49838)">
<path d="M107 82.5L-12.5 14L-11 -11L107 56V82.5Z" fill="url(#paint0_linear_14540_49838)"/>
</g>
<g filter="url(#filter1_n_14540_49838)">
<path d="M118 62.5L-1.5 -6L0 -29L118 38V62.5Z" fill="url(#paint1_linear_14540_49838)"/>
</g>
<g filter="url(#filter2_n_14540_49838)">
<path d="M130 44.5L10.5 -24L12 -43L130 24V44.5Z" fill="url(#paint2_linear_14540_49838)"/>
</g>
<g filter="url(#filter3_n_14540_49838)">
<path d="M134 26.5L14.5 -42L16 -61L134 6V26.5Z" fill="url(#paint3_linear_14540_49838)"/>
</g>
<g filter="url(#filter4_n_14540_49838)">
<path d="M98 103.5L-21.5 35L-20 10L98 77V103.5Z" fill="url(#paint4_linear_14540_49838)"/>
</g>
<g filter="url(#filter5_n_14540_49838)">
<path d="M78 118.5L-41.5 50L-40 25L78 92V118.5Z" fill="url(#paint5_linear_14540_49838)"/>
</g>
<line x1="1.24847" y1="-4.43389" x2="118.248" y2="62.5661" stroke="#BBBBBB"/>
<line x1="-28.7515" y1="30.5661" x2="88.2485" y2="97.5661" stroke="#BBBBBB"/>
<line x1="12.2485" y1="-22.4339" x2="129.248" y2="44.5661" stroke="#BBBBBB"/>
<line x1="32.2485" y1="-32.4339" x2="149.248" y2="34.5661" stroke="#BBBBBB"/>
<line x1="-18.7515" y1="10.5661" x2="98.2485" y2="77.5661" stroke="#BBBBBB"/>
<path d="M-5.02441 46.5973C-3.921 44.6451 -1.29176 44.2197 0.371094 45.7242L0.50293 45.8434C-1.20455 45.6635 -2.96791 46.4657 -3.89453 48.1051L-13.6982 65.4469L-15.2188 64.6324L-5.02441 46.5973ZM18.1055 33.8463C19.1731 31.9677 21.6711 31.4955 23.3506 32.8551L23.6572 33.1031C21.9385 32.9101 20.1649 33.7162 19.2354 35.3521L11.5293 48.9166C10.4231 50.8635 7.79816 51.2851 6.1377 49.7828L6.00488 49.6627C7.70942 49.8426 9.46996 49.0449 10.3984 47.4107L18.1055 33.8463ZM42.1758 20.3014C43.0356 19.9161 44.0356 19.8832 44.9307 20.2467C44.365 20.2895 43.8129 20.4859 43.3438 20.8248C42.7925 21.204 42.3516 21.7592 42.1221 22.4605L34.3223 35.443C33.2969 37.4 30.7368 37.9288 29.0195 36.5387L28.7119 36.2896C30.4817 36.4887 32.3063 35.628 33.208 33.9068L35.9199 28.7281L35.9385 28.7389L40.7852 21.6959C41.1241 21.0752 41.6133 20.6076 42.1758 20.3014ZM64.3164 9.19882C65.4061 7.37533 66.8563 6.9353 68.5127 8.26523L68.8359 8.52402C67.1475 8.33193 66.4041 9.10294 65.458 10.6861L57.7031 24.3375C56.5338 26.2938 54.8365 26.6285 53.2246 25.0172L52.9004 24.693C54.6977 25.0755 55.6689 24.3441 56.71 22.6021L64.3164 9.19882ZM82.9121 -1.62637C83.9914 -3.66959 86.7202 -4.12469 88.4043 -2.54239L88.4844 -2.46719C86.7375 -2.6496 84.9327 -1.8053 84.0283 -0.0931702L78.6514 10.981C77.6244 12.9255 75.0799 13.4525 73.3652 12.0758L73.0449 11.818C74.8104 12.017 76.6305 11.1613 77.5352 9.44882L82.9121 -1.62637Z" fill="#A1A1A1"/>
<path d="M20.9756 61.5973C22.079 59.6451 24.7082 59.2197 26.3711 60.7242L26.5029 60.8434C24.7955 60.6635 23.0321 61.4657 22.1055 63.1051L12.3018 80.4469L10.7812 79.6324L20.9756 61.5973ZM44.1055 48.8463C45.1731 46.9677 47.6711 46.4955 49.3506 47.8551L49.6572 48.1031C47.9385 47.9101 46.1649 48.7162 45.2354 50.3521L37.5293 63.9166C36.4231 65.8635 33.7982 66.2851 32.1377 64.7828L32.0049 64.6627C33.7094 64.8426 35.47 64.0449 36.3984 62.4107L44.1055 48.8463ZM68.1758 35.3014C69.0356 34.9161 70.0356 34.8832 70.9307 35.2467C70.365 35.2895 69.8129 35.4859 69.3438 35.8248C68.7925 36.204 68.3516 36.7592 68.1221 37.4605L60.3223 50.443C59.2969 52.4 56.7368 52.9288 55.0195 51.5387L54.7119 51.2896C56.4817 51.4887 58.3063 50.628 59.208 48.9068L61.9199 43.7281L61.9385 43.7389L66.7852 36.6959C67.1241 36.0752 67.6133 35.6076 68.1758 35.3014ZM90.3164 24.1988C91.4061 22.3753 92.8563 21.9353 94.5127 23.2652L94.8359 23.524C93.1475 23.3319 92.4041 24.1029 91.458 25.6861L83.7031 39.3375C82.5338 41.2938 80.8365 41.6285 79.2246 40.0172L78.9004 39.693C80.6977 40.0755 81.6689 39.3441 82.71 37.6021L90.3164 24.1988ZM108.912 13.3736C109.991 11.3304 112.72 10.8753 114.404 12.4576L114.484 12.5328C112.738 12.3504 110.933 13.1947 110.028 14.9068L104.651 25.981C103.624 27.9255 101.08 28.4525 99.3652 27.0758L99.0449 26.818C100.81 27.017 102.63 26.1613 103.535 24.4488L108.912 13.3736Z" fill="#A1A1A1"/>
<path d="M47.9756 76.5973C49.079 74.6451 51.7082 74.2197 53.3711 75.7242L53.5029 75.8434C51.7955 75.6635 50.0321 76.4657 49.1055 78.1051L39.3018 95.4469L37.7812 94.6324L47.9756 76.5973ZM71.1055 63.8463C72.1731 61.9677 74.6711 61.4955 76.3506 62.8551L76.6572 63.1031C74.9385 62.9101 73.1649 63.7162 72.2354 65.3521L64.5293 78.9166C63.4231 80.8635 60.7982 81.2851 59.1377 79.7828L59.0049 79.6627C60.7094 79.8426 62.47 79.0449 63.3984 77.4107L71.1055 63.8463ZM95.1758 50.3014C96.0356 49.9161 97.0356 49.8832 97.9307 50.2467C97.365 50.2895 96.8129 50.4859 96.3438 50.8248C95.7925 51.204 95.3516 51.7592 95.1221 52.4605L87.3223 65.443C86.2969 67.4 83.7368 67.9288 82.0195 66.5387L81.7119 66.2896C83.4817 66.4887 85.3063 65.628 86.208 63.9068L88.9199 58.7281L88.9385 58.7389L93.7852 51.6959C94.1241 51.0752 94.6133 50.6076 95.1758 50.3014ZM117.316 39.1988C118.406 37.3753 119.856 36.9353 121.513 38.2652L121.836 38.524C120.148 38.3319 119.404 39.1029 118.458 40.6861L110.703 54.3375C109.534 56.2938 107.836 56.6285 106.225 55.0172L105.9 54.693C107.698 55.0755 108.669 54.3441 109.71 52.6021L117.316 39.1988ZM135.912 28.3736C136.991 26.3304 139.72 25.8753 141.404 27.4576L141.484 27.5328C139.738 27.3504 137.933 28.1947 137.028 29.9068L131.651 40.981C130.624 42.9255 128.08 43.4525 126.365 42.0758L126.045 41.818C127.81 42.017 129.63 41.1613 130.535 39.4488L135.912 28.3736Z" fill="#9E9E9E"/>
<path d="M-54.0244 43.5973C-52.921 41.6451 -50.2918 41.2197 -48.6289 42.7242L-48.4971 42.8434C-50.2045 42.6635 -51.9679 43.4657 -52.8945 45.1051L-62.6982 62.4469L-64.2188 61.6324L-54.0244 43.5973ZM-30.8945 30.8463C-29.8269 28.9677 -27.3289 28.4955 -25.6494 29.8551L-25.3428 30.1031C-27.0615 29.9101 -28.8351 30.7162 -29.7646 32.3521L-37.4707 45.9166C-38.5769 47.8635 -41.2018 48.2851 -42.8623 46.7828L-42.9951 46.6627C-41.2906 46.8426 -39.53 46.0449 -38.6016 44.4107L-30.8945 30.8463ZM-6.82422 17.3014C-5.96438 16.9161 -4.96437 16.8832 -4.06934 17.2467C-4.63498 17.2895 -5.18708 17.4859 -5.65625 17.8248C-6.20748 18.204 -6.64837 18.7592 -6.87793 19.4605L-14.6777 32.443C-15.7031 34.4 -18.2632 34.9288 -19.9805 33.5387L-20.2881 33.2896C-18.5183 33.4887 -16.6937 32.628 -15.792 30.9068L-13.0801 25.7281L-13.0615 25.7389L-8.21484 18.6959C-7.87586 18.0752 -7.38674 17.6076 -6.82422 17.3014ZM15.3164 6.19882C16.4061 4.37533 17.8563 3.9353 19.5127 5.26523L19.8359 5.52402C18.1475 5.33193 17.4041 6.10294 16.458 7.68613L8.70312 21.3375C7.53376 23.2938 5.83646 23.6285 4.22461 22.0172L3.90039 21.693C5.69771 22.0755 6.66885 21.3441 7.70996 19.6021L15.3164 6.19882ZM33.9121 -4.62637C34.9914 -6.66959 37.7202 -7.12469 39.4043 -5.54239L39.4844 -5.46719C37.7375 -5.6496 35.9327 -4.8053 35.0283 -3.09317L29.6514 7.98105C28.6244 9.92549 26.0799 10.4525 24.3652 9.07578L24.0449 8.81796C25.8104 9.01703 27.6305 8.16129 28.5352 6.44882L33.9121 -4.62637Z" fill="#9E9E9E"/>
</g>
<rect x="0.5" y="0.5" width="97" height="67" rx="3.5" fill="white" fill-opacity="0.06" style="mix-blend-mode:soft-light"/>
<rect x="0.5" y="0.5" width="97" height="67" rx="3.5" stroke="#BBBBBB"/>
</g>
<defs>
<filter id="filter0_n_14540_49838" x="-12.5" y="-11" width="119.5" height="93.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="0.67114090919494629 0.67114090919494629" stitchTiles="stitch" numOctaves="3" result="noise" seed="6118" />
<feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
<feComponentTransfer in="alphaNoise" result="coloredNoise1">
<feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
</feComponentTransfer>
<feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
<feFlood flood-color="rgba(0, 0, 0, 0.25)" result="color1Flood" />
<feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
<feMerge result="effect1_noise_14540_49838">
<feMergeNode in="shape" />
<feMergeNode in="color1" />
</feMerge>
</filter>
<filter id="filter1_n_14540_49838" x="-1.5" y="-29" width="119.5" height="91.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="0.66666668653488159 0.66666668653488159" stitchTiles="stitch" numOctaves="3" result="noise" seed="2311" />
<feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
<feComponentTransfer in="alphaNoise" result="coloredNoise1">
<feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
</feComponentTransfer>
<feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
<feFlood flood-color="rgba(0, 0, 0, 0.25)" result="color1Flood" />
<feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
<feMerge result="effect1_noise_14540_49838">
<feMergeNode in="shape" />
<feMergeNode in="color1" />
</feMerge>
</filter>
<filter id="filter2_n_14540_49838" x="10.5" y="-43" width="119.5" height="87.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="0.66666668653488159 0.66666668653488159" stitchTiles="stitch" numOctaves="3" result="noise" seed="9109" />
<feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
<feComponentTransfer in="alphaNoise" result="coloredNoise1">
<feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
</feComponentTransfer>
<feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
<feFlood flood-color="rgba(0, 0, 0, 0.25)" result="color1Flood" />
<feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
<feMerge result="effect1_noise_14540_49838">
<feMergeNode in="shape" />
<feMergeNode in="color1" />
</feMerge>
</filter>
<filter id="filter3_n_14540_49838" x="14.5" y="-61" width="119.5" height="87.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="0.66666668653488159 0.66666668653488159" stitchTiles="stitch" numOctaves="3" result="noise" seed="9109" />
<feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
<feComponentTransfer in="alphaNoise" result="coloredNoise1">
<feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
</feComponentTransfer>
<feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
<feFlood flood-color="rgba(0, 0, 0, 0.25)" result="color1Flood" />
<feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
<feMerge result="effect1_noise_14540_49838">
<feMergeNode in="shape" />
<feMergeNode in="color1" />
</feMerge>
</filter>
<filter id="filter4_n_14540_49838" x="-21.5" y="10" width="119.5" height="93.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="0.66666668653488159 0.66666668653488159" stitchTiles="stitch" numOctaves="3" result="noise" seed="2311" />
<feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
<feComponentTransfer in="alphaNoise" result="coloredNoise1">
<feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
</feComponentTransfer>
<feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
<feFlood flood-color="rgba(0, 0, 0, 0.25)" result="color1Flood" />
<feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
<feMerge result="effect1_noise_14540_49838">
<feMergeNode in="shape" />
<feMergeNode in="color1" />
</feMerge>
</filter>
<filter id="filter5_n_14540_49838" x="-41.5" y="25" width="119.5" height="93.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="0.66666668653488159 0.66666668653488159" stitchTiles="stitch" numOctaves="3" result="noise" seed="9109" />
<feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
<feComponentTransfer in="alphaNoise" result="coloredNoise1">
<feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
</feComponentTransfer>
<feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
<feFlood flood-color="rgba(0, 0, 0, 0.25)" result="color1Flood" />
<feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
<feMerge result="effect1_noise_14540_49838">
<feMergeNode in="shape" />
<feMergeNode in="color1" />
</feMerge>
</filter>
<linearGradient id="paint0_linear_14540_49838" x1="52.5" y1="24.5" x2="41" y2="45" gradientUnits="userSpaceOnUse">
<stop stop-color="#989898"/>
<stop offset="0.0336538" stop-color="#BBBBBB"/>
<stop offset="0.216346" stop-color="#F3F3F3"/>
<stop offset="0.350962" stop-color="#F3F3F3"/>
<stop offset="0.870192" stop-color="#BBBBBB"/>
<stop offset="0.975962" stop-color="#8C8C8C"/>
</linearGradient>
<linearGradient id="paint1_linear_14540_49838" x1="63.5" y1="5.74064" x2="52.374" y2="26.0075" gradientUnits="userSpaceOnUse">
<stop stop-color="#989898"/>
<stop offset="0.0336538" stop-color="#BBBBBB"/>
<stop offset="0.216346" stop-color="#F3F3F3"/>
<stop offset="0.350962" stop-color="#F3F3F3"/>
<stop offset="0.870192" stop-color="#BBBBBB"/>
<stop offset="0.975962" stop-color="#8C8C8C"/>
</linearGradient>
<linearGradient id="paint2_linear_14540_49838" x1="75.5" y1="-9.77808" x2="65.1199" y2="9.99435" gradientUnits="userSpaceOnUse">
<stop stop-color="#989898"/>
<stop offset="0.0336538" stop-color="#BBBBBB"/>
<stop offset="0.216346" stop-color="#F3F3F3"/>
<stop offset="0.350962" stop-color="#F3F3F3"/>
<stop offset="0.870192" stop-color="#BBBBBB"/>
<stop offset="0.975962" stop-color="#8C8C8C"/>
</linearGradient>
<linearGradient id="paint3_linear_14540_49838" x1="79.5" y1="-27.7781" x2="69.1199" y2="-8.00565" gradientUnits="userSpaceOnUse">
<stop stop-color="#989898"/>
<stop offset="0.0336538" stop-color="#BBBBBB"/>
<stop offset="0.216346" stop-color="#F3F3F3"/>
<stop offset="0.350962" stop-color="#F3F3F3"/>
<stop offset="0.870192" stop-color="#BBBBBB"/>
<stop offset="0.975962" stop-color="#8C8C8C"/>
</linearGradient>
<linearGradient id="paint4_linear_14540_49838" x1="43.5" y1="45.5" x2="32" y2="66" gradientUnits="userSpaceOnUse">
<stop stop-color="#989898"/>
<stop offset="0.0336538" stop-color="#BBBBBB"/>
<stop offset="0.216346" stop-color="#F3F3F3"/>
<stop offset="0.350962" stop-color="#F3F3F3"/>
<stop offset="0.870192" stop-color="#BBBBBB"/>
<stop offset="0.975962" stop-color="#8C8C8C"/>
</linearGradient>
<linearGradient id="paint5_linear_14540_49838" x1="23.5" y1="60.5" x2="12" y2="81" gradientUnits="userSpaceOnUse">
<stop stop-color="#989898"/>
<stop offset="0.0336538" stop-color="#BBBBBB"/>
<stop offset="0.216346" stop-color="#F3F3F3"/>
<stop offset="0.350962" stop-color="#F3F3F3"/>
<stop offset="0.870192" stop-color="#BBBBBB"/>
<stop offset="0.975962" stop-color="#8C8C8C"/>
</linearGradient>
<clipPath id="clip0_14540_49838">
<rect width="98" height="68" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>
