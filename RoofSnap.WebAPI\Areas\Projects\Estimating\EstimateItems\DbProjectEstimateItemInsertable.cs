﻿using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.Estimating;
using RoofSnap.WebAPI.Common.Data;
using System;

namespace RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems
{
    //    Database model for project estimate item. This is used for create/updating project estimate items.
    //    Our current view for project estimate items, FixedProjectEstimateItem, is non updatable.

    public class DbProjectEstimateItemInsertable : IId<string>, IVersioned
    {
        public string Id { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public DateTimeOffset? UpdatedAt { get; set; }
        public byte[] Version { get; set; }
        public string ProjectId { get; set; }
        public DbUnitType? UnitType { get; set; }
        public DbItemType? ItemType { get; set; }
        public string Description { get; set; }
        public string Image { get; set; }
        public double? Units { get; set; }
        public double? CoveragePerUnit { get; set; }
        public double? Total { get; set; }
        public double? Order { get; set; }
        public long SystemChargeableItemId { get; set; }
        public string ColorId { get; set; }
        public long? OfficePricedChargeableItemId { get; set; }
        public string EstimateCategoryId { get; set; }
        public string EstimateId { get; set; }
        public double? LaborCost { get; set; }
        public double? MaterialCost { get; set; }
        public string MaterialOrderDescription { get; set; }
        public bool? HideOnEstimate { get; set; }
        public bool? HideOnContract { get; set; }
        public bool? HideOnMaterialOrder { get; set; }
        public bool HideOnLaborReport { get; set; }
        public double? TotalPerUnit { get; set; }

        // This property has been added to satisfy the business logic necessary for creating estimate items and to keep this model as clean as possible.
        public bool HasHideOnLaborReportValueSet { get; set; }
        public bool AddedBySystem { get; set; }
        public int? SortOrder { get; set; }

        public virtual DbProjectEstimateItem ProjectEstimateItem { get; set; }

        public DbProjectEstimateItemInsertable()
        {
            Id = Guid.NewGuid().ToString().ToUpperInvariant();
            CreatedAt = DateTimeOffset.UtcNow;
        }
    }
}