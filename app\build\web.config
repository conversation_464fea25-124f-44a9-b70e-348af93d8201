<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <clear />
        <rule name="Redirect to https" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="off" ignoreCase="true" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}{REQUEST_URI}" redirectType="Permanent" appendQueryString="false" />
        </rule>
        <rule name="Redirect signup in prod to marketing" stopProcessing="true">
          <match url=".*signup(\/{0,1})$" />
          <conditions>
            <add input="{HTTP_HOST}" matchType="Pattern" pattern="dev-app.roofsnap.com" ignoreCase="true" negate="true" />
          </conditions>
          <action type="Redirect" url="https://roofsnap.com/free-trial/" redirectType="Permanent" appendQueryString="false" />
        </rule>
        <rule name="React Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_METHOD}" matchType="Pattern" pattern="GET" />
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
    <serverRuntime enabled="true" frequentHitThreshold="1" frequentHitTimePeriod="00:00:20" />
    <staticContent>
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <mimeMap fileExtension=".ico" mimeType="image/x-icon" />
      <mimeMap fileExtension="woff" mimeType="font/woff" />
      <mimeMap fileExtension="woff2" mimeType="font/woff2" />
    </staticContent>
    <httpProtocol>
      <customHeaders>
        <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
        <!-- Note: If our external script dependencies ever change this list will need to be updated -->
        <add name="Content-Security-Policy" value="default-src blob: data: 'unsafe-inline' ___contentSecurityPolicy___" />
        <add name="X-Frame-Options" value="SAMEORIGIN" />
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
        <add name="Permissions-Policy" value="geolocation=(self), midi=(), notifications=(self), push=(self), sync-xhr=(self), microphone=(), camera=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), payment=(self)" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>
