﻿using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.Estimating;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialCategories;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialItems;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialItems.ColorOptions;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems.ProjectEstimateItemRules;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions;
using RoofSnap.WebAPI.Common.Data;
using System;
using System.Collections.Generic;

namespace RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems
{
    public class DbProjectEstimateItem : IId<string>, IVersioned
    {
        public string Id { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public DateTimeOffset? UpdatedAt { get; set; }
        public byte[] Version { get; set; }
        public string ProjectId { get; set; }
        public DbUnitType? UnitType { get; set; }
        public DbItemType? ItemType { get; set; }
        public string Description { get; set; }
        public string Image { get; set; }
        public double Units { get; set; }
        public double? CoveragePerUnit { get; set; }
        public double? Total { get; set; }
        public long SystemChargeableItemId { get; set; }
        public long? OfficePricedChargeableItemId { get; set; }
        public long? CategoryId { get; set; }
        public string OfficeCustomCategoryId { get; set; }
        public string EstimateId { get; set; }
        public double? Labor { get; set; }
        public double? Material { get; set; }
        public string MaterialOrderDescription { get; set; }
        public bool? HideOnEstimate { get; set; }
        public bool? HideOnContract { get; set; }
        public bool? HideOnMaterialOrder { get; set; }
        public bool HideOnLaborReport { get; set; }
        public double? TotalPerUnit { get; set; }
        public string ChargeableItemColorId { get; set; }
        public string OfficePricedChargeableItemColorOptionId { get; set; }
        public bool AddedBySystem { get; set; }
        public int? SortOrder { get; set; }
        public virtual DbProject Project { get; set; }
        public virtual DbCategory Category { get; set; }
        public virtual DbOfficeCustomCategory OfficeCustomCategory { get; set; }
        public virtual DbOfficePricedChargeableItem OfficePricedChargeableItem { get; set; }
        public virtual DbChargeableItemColor ChargeableItemColor { get; set; }
        public virtual DbOfficePricedChargeableItemColorOption OfficePricedChargeableItemColorOption { get; set; }
        public virtual DbProjectEstimateItemInsertable ProjectEstimateItemInsertable { get; set; }
        public virtual DbProjectEstimateOption ProjectEstimateOption { get; set; }

        public Dictionary<ProjectEstimateItemRuleName, bool> Attributes { get; set; }

        public DbProjectEstimateItem()
        {
            Id = Guid.NewGuid().ToString().ToUpperInvariant();
        }
    }
}