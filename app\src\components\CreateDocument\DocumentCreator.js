import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { EstimateType } from 'lib/Models/Estimate.ts';
import { getDocuments } from '../../actions/documentsActions';
import ChooseSingleEstimateDocumentDialog from './ChooseSingleEstimateDocumentDialog';
import ChooseMultiEstimateDialog from '../ChooseMultiEstimateDialog';
import { DocumentDataContextType } from '../../lib/documentDataContextType';
import { getDocumentTemplateCategoryByV1Type } from '../../lib/documentTemplateV1TypeToCategory';
import LoggerWrapper from '../../lib/Logger';
import {
    createDocument,
    CREATE_DOCUMENT_FAILURE,
} from '../../actions/documentActions';
import { setReduxMessage } from '../../actions/ReduxMessagesActions';
import DocumentCategoryNames from '../../lib/DocumentsCategories.ts';
import FormHtmlDialog from './FormHtmlDialog.tsx';

class DocumentCreator extends Component {
    state = {
        chooseSingleEstimateDialogIsOpen: false,
        selectMultiEstimateDialogIsOpen: false,
        selectedDocumentTemplate: {},
        formHtmlDialogIsOpen: false,
        selectedDocumentTemplateCategoryName: '',
        selectedDocumentId: '',
    };

    getDataContextEntityId = (documentDataContextType) => {
        switch (documentDataContextType) {
            case DocumentDataContextType.Project:
            case DocumentDataContextType.ProjectDrawing:
                return this.props.projectId;
            default:
                return null;
        }
    };

    getItemizedEstimate = () =>
        this.props.estimates.filter(
            (estimate) => estimate.estimateType === EstimateType.Itemized
        );

    generating = false;

    openChooseSingleEstimateDocumentDialog = () => {
        this.setState({
            chooseSingleEstimateDialogIsOpen: true,
            selectMultiEstimateDialogIsOpen: false,
        });
    };

    openChooseMultiEstimateDialog = () => {
        this.setState({
            selectMultiEstimateDialogIsOpen: true,
            chooseSingleEstimateDialogIsOpen: false,
        });
    };

    openFormHtmlDialog = () => {
        this.setState({
            formHtmlDialogIsOpen: true,
        });
    };

    resetState = () => {
        this.setState({
            chooseSingleEstimateDialogIsOpen: false,
            selectMultiEstimateDialogIsOpen: false,
            selectedDocumentTemplate: {},
            formHtmlDialogIsOpen: false,
            selectedDocumentTemplateCategoryName: '',
            selectedDocumentId: '',
        });
        this.generating = false;
    };

    selectEstimate = async (estimateId) => {
        if (
            this.state.selectedDocumentTemplate.hasHtmlForm &&
            this.state.selectedDocumentTemplate.usesLegacySigningWorkflow
        ) {
            this.setState({ chooseSingleEstimateDialogIsOpen: false });
            this.openFormHtmlDialog();
            return;
        }

        await this.props.onStartCreateDocument(estimateId);
    };

    handleMultiEstimateConfirmClick = async (estimateOptionIds) => {
        await this.props.onStartCreateDocument(null, estimateOptionIds);
    };

    handleFormHtmlDialogRequestClose = async () => {
        this.props.dispatch(getDocuments(this.props.projectId));
        this.resetState();
    };

    handleSelectDocumentTemplate = async (template, categoryName) => {
        if (this.generating) {
            return;
        }
        this.generating = true;
        this.setState({
            selectedDocumentTemplate: template,
            selectedDocumentTemplateCategoryName: categoryName,
        });

        if (
            template.documentDataContextType ===
            DocumentDataContextType.Estimate
        ) {
            if (this.getItemizedEstimate().length === 1) {
                if (template.usesLegacySigningWorkflow) {
                    this.openFormHtmlDialog();
                    this.generating = false;
                    return;
                }

                if (this.props.onSetTemplate)
                    await this.props.onSetTemplate(template);

                const estimateId = this.getItemizedEstimate()[0].id;
                await this.props.onStartCreateDocument(estimateId);
            } else {
                this.openChooseSingleEstimateDocumentDialog();
            }

            try {
                if (categoryName === DocumentCategoryNames.MaterialOrder) {
                    window.Appcues.track('Created Material Order Document');
                } else {
                    window.Appcues.track('Created Estimate Document');
                }
            } catch (error) {
                LoggerWrapper.captureException(error);
            }

            this.generating = false;
            return;
        }

        if (
            template.documentDataContextType ===
            DocumentDataContextType.MultiEstimate
        ) {
            this.openChooseMultiEstimateDialog();
            this.generating = false;
            return;
        }

        if (this.props.onSetTemplate) await this.props.onSetTemplate(template);

        if (template.hasHtmlForm) {
            this.openFormHtmlDialog();
            this.generating = false;
            return;
        }

        const dataContextEntityId = this.getDataContextEntityId(
            template.documentDataContextType
        );
        await this.props.onStartCreateDocument(dataContextEntityId);
    };

    startCreateDocument = async (
        template,
        dataContextEntityId,
        estimateOptionIds,
        resetStateAtEnd
    ) => {
        if (!this.props.organizationId) {
            this.props.dispatch(
                setReduxMessage(
                    'Sorry, the document failed to be created. Please try again.'
                )
            );
            return;
        }

        const hasV2HtmlForm =
            !template.usesLegacySigningWorkflow && template.hasHtmlForm;

        const params = {
            organizationId: this.props.organizationId,
            templateId: template.id,
            dataContextEntityId,
            estimateOptionIds,
            autoCreateRendering: !hasV2HtmlForm,
            name: this.props.name
        };
        const result = await this.props.dispatch(createDocument(params));
        if (result.type === CREATE_DOCUMENT_FAILURE) {
            this.props.dispatch(
                setReduxMessage(
                    'Sorry, the document failed to be created. Please try again.'
                )
            );
            return;
        }

        if (hasV2HtmlForm) {
            this.setState({
                selectedDocumentId: result.response.id,
            });
            this.openFormHtmlDialog();
        } else if (resetStateAtEnd) {
            this.resetState();
        }
    };

    render() {
        const v1TemplateCategory = getDocumentTemplateCategoryByV1Type(
            this.state.selectedDocumentTemplateCategoryName
        );

        return (
            <>
                <ChooseSingleEstimateDocumentDialog
                    estimates={this.getItemizedEstimate()}
                    open={this.state.chooseSingleEstimateDialogIsOpen}
                    onListItemClick={this.selectEstimate}
                    onDismissClick={() => {
                        this.resetState();
                        this.props.onEstimateDismiss();
                    }}
                />

                <ChooseMultiEstimateDialog
                    estimates={this.props.estimates}
                    open={this.state.selectMultiEstimateDialogIsOpen}
                    onConfirmClick={this.handleMultiEstimateConfirmClick}
                    onDismissClick={() => {
                        this.resetState();
                        this.props.onEstimateDismiss();
                    }}
                />

                <FormHtmlDialog
                    open={this.state.formHtmlDialogIsOpen}
                    onRequestClose={this.handleFormHtmlDialogRequestClose}
                    projectId={this.props.projectId}
                    category={v1TemplateCategory}
                    estimateId={this.state.selectedEstimateId}
                    usesLegacySigning={
                        this.state.selectedDocumentTemplate
                            .usesLegacySigningWorkflow
                    }
                    documentId={this.state.selectedDocumentId}
                />
            </>
        );
    }
}

const estimateProps = {
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    grandTotal: PropTypes.number.isRequired,
};

DocumentCreator.defaultProps = {
    onSetTemplate: null,
};

DocumentCreator.propTypes = {
    estimates: PropTypes.arrayOf(PropTypes.shape(estimateProps)).isRequired,
    onEstimateDismiss: PropTypes.func.isRequired,
    organizationId: PropTypes.number.isRequired,
    dispatch: PropTypes.func.isRequired,
    projectId: PropTypes.string.isRequired,
    onSetTemplate: PropTypes.func,
    onStartCreateDocument: PropTypes.func.isRequired,
    name: PropTypes.string.isRequired,
};

export default DocumentCreator;
