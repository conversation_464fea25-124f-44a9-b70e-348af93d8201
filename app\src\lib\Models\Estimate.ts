import { EstimateItem } from './EstimateItem';

export type Estimate = {
    id: string;
    projectId: string;
    name: string;
    estimateType: EstimateType;
    roofMaterialName: string;
    subTotal: number;
    taxes: number;
    grandTotal: number;
    discount: number;
    markupDescription: string;
    markupPercentage: number;
    createdAt: string;
    updatedAt: string;
    isChosen: boolean;
    version: string;
    estimateItems: EstimateItem[];
    status: EstimateStatus;
    totalCostPerSquare: string;
};

export enum EstimateType {
    Snap = 'Snap',
    Itemized = 'Itemized',
}

export enum EstimateStatus {
    InProgress = 'In Progress',
    Saved = 'Saved',
    Failed = 'Failed',
}
