﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems
{
    public class ProjectEstimateItemConfiguration : EntityTypeConfiguration<DbProjectEstimateItem>
    {
        public ProjectEstimateItemConfiguration()
            : this("roofsnaplive")
        {
        }

        public ProjectEstimateItemConfiguration(string schema)
        {
            ToTable("FixedProjectEstimateItems", schema);
            HasKey(x => new { x.Id });

            Property(x => x.Id).HasColumnName(@"Id").IsRequired().HasColumnType("nvarchar").HasMaxLength(255)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            Property(x => x.CreatedAt).HasColumnName(@"__createdAt").IsRequired().HasColumnType("datetimeoffset");
            Property(x => x.UpdatedAt).HasColumnName(@"__updatedAt").IsOptional().HasColumnType("datetimeoffset");
            Property(x => x.Version).HasColumnName(@"__version").IsRequired().IsFixedLength().HasColumnType("timestamp").HasMaxLength(8).IsRowVersion().HasDatabaseGeneratedOption(DatabaseGeneratedOption.Computed);
            Property(x => x.ProjectId).HasColumnName(@"projectid").IsOptional().HasColumnType("nvarchar").HasMaxLength(255);
            Property(x => x.UnitType).HasColumnName(@"unittype").IsOptional().HasColumnType("int");
            Property(x => x.ItemType).HasColumnName(@"itemtype").IsOptional().HasColumnType("int");
            Property(x => x.Description).HasColumnName(@"description").IsOptional().HasColumnType("nvarchar(max)");
            Property(x => x.Image).HasColumnName(@"image").IsOptional().HasColumnType("nvarchar(max)");
            Property(x => x.Units).HasColumnName(@"units").HasColumnType("float");
            Property(x => x.CoveragePerUnit).HasColumnName(@"coverageperunit").IsOptional().HasColumnType("float");
            Property(x => x.Total).HasColumnName(@"Total").IsOptional().HasColumnType("float");
            Property(x => x.SystemChargeableItemId).HasColumnName(@"systemchargeableitemid").IsRequired().HasColumnType("bigint");
            Property(x => x.OfficePricedChargeableItemId).HasColumnName(@"OfficePricedChargeableItemId").IsOptional().HasColumnType("bigint");
            Property(x => x.CategoryId).HasColumnName(@"CategoryId").IsOptional().HasColumnType("bigint");
            Property(x => x.OfficeCustomCategoryId).HasColumnName(@"OfficeCustomCategoryId").IsOptional().HasColumnType("nvarchar").HasMaxLength(255);
            Property(x => x.EstimateId).HasColumnName(@"estimateid").IsOptional().HasColumnType("nvarchar(max)");
            Property(x => x.Labor).HasColumnName(@"Labor").IsOptional().HasColumnType("float");
            Property(x => x.Material).HasColumnName(@"Material").IsOptional().HasColumnType("float");
            Property(x => x.MaterialOrderDescription).HasColumnName(@"materialorderdescription").IsOptional().HasColumnType("nvarchar(max)");
            Property(x => x.HideOnEstimate).HasColumnName(@"hideonestimate").IsOptional().HasColumnType("bit");
            Property(x => x.HideOnContract).HasColumnName(@"hideoncontract").IsOptional().HasColumnType("bit");
            Property(x => x.HideOnMaterialOrder).HasColumnName(@"hideonmaterialorder").IsOptional().HasColumnType("bit");
            Property(x => x.HideOnLaborReport).HasColumnName(@"HideOnLaborReport").IsRequired().HasColumnType("bit");
            Property(x => x.TotalPerUnit).HasColumnName(@"totalperunit").IsOptional().HasColumnType("float");
            Property(x => x.ChargeableItemColorId).HasColumnName(@"ChargeableItemColorId").HasColumnType("nvarchar")
                .IsOptional().HasMaxLength(255);
            Property(x => x.SortOrder).HasColumnName(@"SortOrder").IsOptional().HasColumnType("int");
            Property(x => x.OfficePricedChargeableItemColorOptionId)
                .HasColumnName(@"OfficePricedChargeableItemColorOptionId").HasColumnType("nvarchar").IsOptional()
                .HasMaxLength(255);
            Property(x => x.AddedBySystem).HasColumnName(@"addedBySystem").IsRequired().HasColumnType("bit");

            HasOptional(x => x.Category)
                .WithMany(x => x.ProjectEstimateItems)
                .HasForeignKey(x => x.CategoryId)
                .WillCascadeOnDelete(false);

            HasOptional(x => x.OfficeCustomCategory)
                .WithMany(x => x.ProjectEstimateItems)
                .HasForeignKey(x => x.OfficeCustomCategoryId)
                .WillCascadeOnDelete(false);

            HasOptional(x => x.OfficePricedChargeableItem)
                .WithMany(x => x.ProjectEstimateItems)
                .HasForeignKey(x => x.OfficePricedChargeableItemId)
                .WillCascadeOnDelete(false);

            HasOptional(x => x.ChargeableItemColor)
                .WithMany(x => x.ProjectEstimateItems)
                .HasForeignKey(x => x.ChargeableItemColorId)
                .WillCascadeOnDelete(false);

            HasOptional(x => x.OfficePricedChargeableItemColorOption)
                .WithMany(x => x.ProjectEstimateItems)
                .HasForeignKey(x => x.OfficePricedChargeableItemColorOptionId)
                .WillCascadeOnDelete(false);

            HasRequired(x => x.ProjectEstimateItemInsertable)
                .WithRequiredPrincipal(x => x.ProjectEstimateItem)
                .WillCascadeOnDelete(false);

            HasOptional(x => x.ProjectEstimateOption)
                .WithMany(x => x.ProjectEstimateItems)
                .HasForeignKey(x => x.EstimateId)
                .WillCascadeOnDelete(false);

            HasRequired(a => a.Project)
                .WithMany(b => b.ProjectEstimateItems)
                .HasForeignKey(c => c.ProjectId)
                .WillCascadeOnDelete(false);
        }
    }
}