﻿using RoofSnap.Core.Data;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialCategories;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialItems;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialItems.ColorOptions;
using RoofSnap.WebAPI.Areas.Offices.Estimating.Templates;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems.CreateEstimateItem;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems.ProjectEstimateItemRules;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions.EstimateTotalCalculators;
using RoofSnap.WebAPI.Areas.Projects.Estimating.UnitQuantityCalculation;
using RoofSnap.WebAPI.Areas.Projects.ProjectDrawings;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.Config;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.Logging;
using RoofSnap.WebAPI.Common.WebAPI;
using RoofSnap.WebAPI.Common.Wrappers;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace RoofSnap.WebAPI.Areas.Projects.Estimating
{
    public interface IEstimateService
    {
        Task<DbProjectEstimateItem> CreateItemAsync(string projectId, string estimateId, DbProjectEstimateItemInsertable dbProjectEstimateItemInsertable);
        Task<DbProjectEstimateOption> CreateAsync(string projectIdOrShortCode, DbProjectEstimateOption dbProjectEstimateOption);
        Task DeleteAsync(string projectIdOrShortCode, string estimateId);
        Task DeleteItemAsync(string projectIdOrShortCode, string projectEstimateItemId);
        Task<DbProjectEstimateItem> GetItemAsync(string projectIdOrShortCode, string estimateItemId);
        Task<IList<DbProjectEstimateItem>> GetItemsAsync(string projectIdOrShortCode, PagingInfo pagingInfo);
        Task<IList<DbProjectEstimateItem>> GetItemsFromEstimateAsync(string projectIdOrShortCode, string estimateOptionId, PagingInfo pagingInfo = null);
        Task<DbProjectEstimateOption> GetAsync(string projectIdOrShortCode, string estimateOptionId);
        Task<IList<DbProjectEstimateOption>> GetByProjectIdAsync(string projectIdOrShortCode, PagingInfo pagingInfo);
        DbProjectEstimateOption Update(DbProjectEstimateOption updatedEstimateOption, byte[] version);
        Task CalculateProjectEstimateOptionTotalsAsync(string projectEstimateOptionId);
        /// <summary>
        /// Gets a DbProjectEstimateItemInsertable object.
        /// Only use this when you need to update and estimate item!
        /// </summary>
        /// <param name="projectIdOrShortCode">Project identifier</param>
        /// <param name="projectEstimateItemId">Estimate item identifier</param>
        /// <returns></returns>
        Task<DbProjectEstimateItemInsertable> GetEstimateItemInsertableAsync(string projectIdOrShortCode,
            string projectEstimateItemId);
        Task<DbProjectEstimateItem> UpdateEstimateItemAsync(long? officeId,
            DbProjectEstimateItemInsertable dbProjectEstimateItemInsertable, byte[] version);
        Task<DbProjectEstimateOption> CreateAndSaveBackwardsCompatibleEstimateOptionAsync(string projectId);
        Task AppyIncidentalsAsync(string estimateId);
        Task UpdateSortOrder(List<EstimateSortOrder> estimateSortOrders);
    }

    public class EstimateService : IEstimateService
    {
        private readonly IOfficeCategorySettingsInitializer _officeCategorySettingsInitializer;
        private readonly IProjectEstimateItemInsertableFactory _projectEstimateItemInsertableFactory;
        private readonly IEstimateItemsFactory _estimateItemsFactory;
        private readonly IErrorLogger _errorLogger;
        private readonly IDateTimeOffsetWrapper _dateTimeOffsetWrapper;
        private readonly IProjectEstimateChooser _projectEstimateChooser;
        private readonly ICreateEstimateItemStrategyFactory _createEstimateItemStrategyFactory;
        private readonly IEstimateTotalCalculator _estimateTotalCalculator;
        private readonly IProjectEstimateItemRuleEvaluator _projectEstimateItemRuleEvaluator;
        private readonly IDbProjectEstimateItemInsertableListFactory _dbProjectEstimateItemInsertableListFactory;
        private readonly IEstimateItemColorOptionResolver _estimateItemColorOptionResolver;
        private readonly IRoofSnapLiveDbContext _dbContext;
        private readonly IRoofSnapData _roofsnapData;
        private readonly IConfig _config;
        private readonly IOfficePricedChargeableItemsUnitsCalculator _officePricedChargeableItemsUnitsCalculator;


        public EstimateService(IProjectEstimateChooser projectEstimateChooser,
            IOfficeCategorySettingsInitializer officeCategorySettingsInitializer,
            IProjectEstimateItemInsertableFactory projectEstimateItemInsertableFactory,
            IDateTimeOffsetWrapper dateTimeOffsetWrapper,
            ICreateEstimateItemStrategyFactory createEstimateItemStrategyFactory,
            IEstimateTotalCalculator estimateTotalCalculator,
            IProjectEstimateItemRuleEvaluator projectEstimateItemRuleEvaluator,
            IDbProjectEstimateItemInsertableListFactory dbProjectEstimateItemInsertableListFactory,
            IEstimateItemColorOptionResolver estimateItemColorOptionResolver,
            IEstimateItemsFactory estimateItemsFactory,
            IErrorLogger errorLogger,
            IRoofSnapLiveDbContext dbContext,
            IRoofSnapData roofsnapData,
            IConfig config,
            IOfficePricedChargeableItemsUnitsCalculator officePricedChargeableItemsUnitsCalculator)
        {
            _projectEstimateChooser = projectEstimateChooser;
            _officeCategorySettingsInitializer = officeCategorySettingsInitializer;
            _projectEstimateItemInsertableFactory = projectEstimateItemInsertableFactory;
            _dateTimeOffsetWrapper = dateTimeOffsetWrapper;
            _createEstimateItemStrategyFactory = createEstimateItemStrategyFactory;
            _estimateTotalCalculator = estimateTotalCalculator;
            _projectEstimateItemRuleEvaluator = projectEstimateItemRuleEvaluator;
            _dbProjectEstimateItemInsertableListFactory = dbProjectEstimateItemInsertableListFactory;
            _estimateItemColorOptionResolver = estimateItemColorOptionResolver;
            _estimateItemsFactory = estimateItemsFactory;
            _errorLogger = errorLogger;
            _dbContext = dbContext;
            _config = config;
            _roofsnapData = roofsnapData;
            _officePricedChargeableItemsUnitsCalculator = officePricedChargeableItemsUnitsCalculator;
        }

        public async Task DeleteAsync(string projectIdOrShortCode, string estimateId)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            DbProjectEstimateOption dbEstimateOption =
                await _dbContext.Entry(dbProject)
                .Collection(p => p.ProjectEstimateOptions)
                .Query()
                .FirstOrDefaultAsync(estimate => estimate.Id == estimateId);

            if (dbEstimateOption != null)
            {
                DbProject project = dbEstimateOption.Project;
                _dbContext.ProjectEstimateOptions.Remove(dbEstimateOption);
                _projectEstimateChooser.EnsureProjectHasAChosenEstimate(project);
            }
        }

        public async Task DeleteItemAsync(string projectIdOrShortCode, string projectEstimateItemId)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (dbProject == null)
                return;

            DbProjectEstimateItem dbProjectEstimateItem = await _dbContext.Entry(dbProject)
                .Collection(p => p.ProjectEstimateItems)
                .Query()
                .FirstOrDefaultAsync(projectEstimateItem => projectEstimateItem.Id == projectEstimateItemId);

            if (dbProjectEstimateItem == null)
                return;

            DbProjectDrawing dbProjectDrawing = await _dbContext.Entry(dbProject)
                .Collection(p => p.ProjectDrawings)
                .Query()
                .FirstOrDefaultAsync(projectDrawing => projectDrawing.Projectid == dbProject.Id);

            bool? canDelete = _projectEstimateItemRuleEvaluator.Evaluate(dbProjectEstimateItem, dbProjectDrawing,
                ProjectEstimateItemRuleName.CanDelete);

            if (canDelete.HasValue && canDelete == false)
                throw new ProjectEstimateItemCannotDeleteException();

            DbProjectEstimateItemInsertable estimateItemInsertable =
                await _dbContext.ProjectEstimateItemsInsertable.FirstOrDefaultAsync(est => est.Id == dbProjectEstimateItem.Id);

            if (estimateItemInsertable == null)
                return;

            _dbContext.ProjectEstimateItemsInsertable.Remove(estimateItemInsertable);
            await _dbContext.SaveChangesAsync();
            await CalculateProjectEstimateOptionTotalsAsync(estimateItemInsertable.EstimateId);

            var incidental = _roofsnapData.EstimateRules.Where(x => dbProject.OfficeId == x.OfficeId && x.Name == estimateItemInsertable.Description);
            if (!incidental.Any())
            {
                await AppyIncidentalsAsync(estimateItemInsertable.EstimateId);
            }
        }

        public async Task<IList<DbProjectEstimateItem>> GetItemsAsync(string projectIdOrShortCode, PagingInfo pagingInfo)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (dbProject == null)
                return null;

            IQueryable<DbProjectEstimateItem> dbProjectEstimateItemQueryable = await GetEstimateItemsAsync(dbProject);

            if (dbProjectEstimateItemQueryable == null)
                return null;

            IList<DbProjectEstimateItem> estimateItems = await dbProjectEstimateItemQueryable
                .OrderBy(estimateItem => estimateItem.CreatedAt)
                .AsPagedAsync(pagingInfo);

            await SetEstimateItemRulesAsync(dbProject, estimateItems.ToArray());

            return estimateItems;
        }

        public async Task<IList<DbProjectEstimateItem>> GetItemsFromEstimateAsync(string projectIdOrShortCode, string estimateOptionId, PagingInfo pagingInfo = null)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (dbProject == null)
                return null;

            IQueryable<DbProjectEstimateItem> dbProjectEstimateItemQuaryable = await GetEstimateItemsAsync(dbProject);

            if (dbProjectEstimateItemQuaryable == null)
                return null;

            DbProjectEstimateOption dbProjectEstimateOption = await _dbContext.Entry(dbProject)
                .Collection(project => project.ProjectEstimateOptions)
                .Query()
                .FirstOrDefaultAsync(projectEstimateOption => projectEstimateOption.Id == estimateOptionId);

            if (dbProjectEstimateOption == null)
                return null;

            IOrderedQueryable<DbProjectEstimateItem> estimateItemsQueryable = dbProjectEstimateItemQuaryable
                .Where(estimateItem => estimateItem.EstimateId == estimateOptionId)
                .OrderBy(estimateItem => estimateItem.CreatedAt);

            IList<DbProjectEstimateItem> estimateItems = pagingInfo != null
                ? await estimateItemsQueryable.AsPagedAsync(pagingInfo)
                : await estimateItemsQueryable.ToListAsync();

            await SetEstimateItemRulesAsync(dbProject, estimateItems.ToArray());

            return estimateItems;
        }

        public async Task<DbProjectEstimateItem> GetItemAsync(string projectIdOrShortCode, string estimateItemId)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (dbProject == null)
                return null;

            IQueryable<DbProjectEstimateItem> dbProjectEstimateItemQuaryable = await GetEstimateItemsAsync(dbProject);

            if (dbProjectEstimateItemQuaryable == null)
                return null;

            DbProjectEstimateItem estimateItem = await dbProjectEstimateItemQuaryable
                .Include(projectEstiamteItem => projectEstiamteItem.OfficePricedChargeableItem)
                .Where(projectEstimateItem => projectEstimateItem.Id == estimateItemId)
                .FirstOrDefaultAsync();

            await SetEstimateItemRulesAsync(dbProject, estimateItem);

            return estimateItem;
        }

        public async Task<DbProjectEstimateItemInsertable> GetEstimateItemInsertableAsync(string projectIdOrShortCode,
            string projectEstimateItemId)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            if (dbProject == null)
                return null;

            var dbProjectEstiamteItem = await _dbContext.Entry(dbProject)
                .Collection(p => p.ProjectEstimateItems)
                .Query()
                .Include(projectEstimateItem => projectEstimateItem.ProjectEstimateItemInsertable)
                .FirstOrDefaultAsync(projectEstimateItem => projectEstimateItem.Id == projectEstimateItemId);

            return dbProjectEstiamteItem?.ProjectEstimateItemInsertable;
        }

        private async Task<IQueryable<DbProjectEstimateItem>> GetEstimateItemsAsync(DbProject dbProject)
        {
            if (dbProject == null)
                return null;

            await _officeCategorySettingsInitializer.InitializeOfficeCategorySettingsIfNeededAsync(dbProject.OfficeId);

            return _dbContext
                .Entry(dbProject)
                .Collection(p => p.ProjectEstimateItems)
                .Query()
                .Include(estimateItem => estimateItem.OfficePricedChargeableItem)
                .Include(estimateItem => estimateItem.OfficePricedChargeableItem.OfficeChargeCategory)
                .Include(estimateItem => estimateItem.OfficePricedChargeableItem.Office)
                .Include(estimateItem => estimateItem.OfficePricedChargeableItem.Office.OfficeCategorySettings)
                .AsQueryable();
        }


        private async Task<DbProjectEstimateItemInsertable> CreateEstimateItemInsertableAsync(DbProject dbProject,
            DbProjectEstimateOption dbProjectEstimateOption, DbProjectEstimateItemInsertable dbProjectEstimateItemInsertable)
        {
            IQueryable<DbOfficePricedChargeableItem> dbOfficePricedChargeableItemQueryable =
                _dbContext.OfficePricedChargeableItems.Where(x =>
                    x.Id == dbProjectEstimateItemInsertable.OfficePricedChargeableItemId &&
                    x.OfficeId == dbProject.OfficeId);

            ICreateEstimateItemStrategy createEstimateItemStrategy =
                _createEstimateItemStrategyFactory.Create(dbProjectEstimateItemInsertable);

            DbProjectEstimateItemInsertable newDbProjectEstimateItemInsertable =
                await createEstimateItemStrategy.CreateEstimateItemAsync(
                    dbProjectEstimateOption,
                    dbProject,
                    dbOfficePricedChargeableItemQueryable,
                    dbProjectEstimateItemInsertable);

            return newDbProjectEstimateItemInsertable;
        }

        public async Task<DbProjectEstimateItem> CreateItemAsync(string projectIdOrShortCode, string estimateId,
            DbProjectEstimateItemInsertable dbProjectEstimateItemInsertable)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);
            if (dbProject == null)
                return null;
            DbProjectEstimateOption dbProjectEstimateOption = await _dbContext.Entry(dbProject)
                .Collection(x => x.ProjectEstimateOptions)
                .Query()
                .FirstOrDefaultAsync(projectEstimateOption => projectEstimateOption.Id == estimateId);

            DbProjectEstimateItemInsertable newDbProjectEstimateItemInsertable = await CreateEstimateItemInsertableAsync(dbProject, dbProjectEstimateOption, dbProjectEstimateItemInsertable);
            _dbContext.ProjectEstimateItemsInsertable.Add(newDbProjectEstimateItemInsertable);

            await _dbContext.SaveChangesAsync();

            // Don't return 'insertables'. Get the read version of the estimate item to return.
            DbProjectEstimateItem dbFixedProjectEstimateItem = await _dbContext.ProjectEstimateItems
                .Include(x => x.OfficePricedChargeableItem)
                .FirstAsync(x => x.Id == newDbProjectEstimateItemInsertable.Id);

            await CalculateProjectEstimateOptionTotalsAsync(estimateId);
            await AppyIncidentalsAsync(estimateId);
            return dbFixedProjectEstimateItem;
        }

        public async Task<DbProjectEstimateItem> UpdateEstimateItemAsync(long? officeId,
            DbProjectEstimateItemInsertable dbProjectEstimateItemInsertable, byte[] version)
        {
            DateTimeOffset now = _dateTimeOffsetWrapper.Now;
            dbProjectEstimateItemInsertable.UpdatedAt = now;

            // check if we should round to orderable quantities
            if (officeId != null && dbProjectEstimateItemInsertable.OfficePricedChargeableItemId != null)
            {
                var dbOffice = await _dbContext.Offices.FirstOrDefaultAsync(o => o.Id == officeId);
                if (dbOffice != null && dbOffice.RoundMaterialsToOrderableQuantities)
                {
                    DbOfficePricedChargeableItem officePricedChargeableItem = await _dbContext.OfficePricedChargeableItems
                        .Include(opci => opci.ChargeableItem)
                        .Include(opci => opci.Office)
                        .AsNoTracking()
                        .FirstOrDefaultAsync(opci => opci.Id == dbProjectEstimateItemInsertable.OfficePricedChargeableItemId);

                    if (officePricedChargeableItem != null && officePricedChargeableItem.Coverage != dbProjectEstimateItemInsertable.CoveragePerUnit)
                    {
                        // do not want to persist this just using it temporarily to calculate the new quantity
                        officePricedChargeableItem.Coverage = dbProjectEstimateItemInsertable.CoveragePerUnit.Value;

                        var measurements = await _dbContext.ProjectMeasurements.FirstOrDefaultAsync(pm => pm.Id == dbProjectEstimateItemInsertable.ProjectId);

                        dbProjectEstimateItemInsertable.Units =
                            _officePricedChargeableItemsUnitsCalculator.GetUnitsForOfficePricedChargeableItem(
                                officePricedChargeableItem, measurements) ?? dbProjectEstimateItemInsertable.Units;
                    }
                }
            }

            dbProjectEstimateItemInsertable.Total =
                (dbProjectEstimateItemInsertable.Units ?? 0.0) * (dbProjectEstimateItemInsertable.TotalPerUnit ?? 0.0);
            if (!string.IsNullOrEmpty(dbProjectEstimateItemInsertable.ColorId) && officeId != null)
            {

                DbOfficePricedChargeableItem materialItem = await _dbContext.OfficePricedChargeableItems
                    .Include(opci => opci.ChargeableItem)
                    .Include(opci => opci.ChargeableItem.ChargeableItemColors)
                    .Include(opci => opci.ChargeableItem.ChargeableItemColors.Select(cic => cic.OfficeHiddenImages))
                    .FirstOrDefaultAsync(opci => opci.Id == dbProjectEstimateItemInsertable.OfficePricedChargeableItemId && opci.OfficeId == officeId);

                ColorOption colorOption = _estimateItemColorOptionResolver.GetColorOptions(materialItem, (long)officeId)
                    .FirstOrDefault(co => co.Id == dbProjectEstimateItemInsertable.ColorId);

                if (colorOption != null)
                {
                    dbProjectEstimateItemInsertable.Image = !string.IsNullOrEmpty(colorOption.Image) ? Flurl.Url.Combine(_config.ImageBaseUrl, colorOption.Image) : string.Empty;
                }
            }

            _dbContext.SetVersion(dbProjectEstimateItemInsertable, version);
            await _dbContext.SaveChangesAsync();

            DbProject dbProject =
                await _dbContext.Projects.GetByIdOrShortCodeAsync(dbProjectEstimateItemInsertable.ProjectId);

            // changes to DbProjectEstimateItemInsertable are not reflected in the view for DbProjectEstimateItem so, need to re-query to get the updated item
            DbProjectEstimateItem dbProjectEstimateItem = await _dbContext
                .Entry(dbProject)
                .Collection(project => project.ProjectEstimateItems)
                .Query()
                .AsNoTracking()
                .Include(projectEstimateItem => projectEstimateItem.OfficePricedChargeableItem)
                .FirstOrDefaultAsync(projectEstiamteItem => projectEstiamteItem.Id == dbProjectEstimateItemInsertable.Id);

            await CalculateProjectEstimateOptionTotalsAsync(dbProjectEstimateItem.EstimateId);
            await AppyIncidentalsAsync(dbProjectEstimateItemInsertable.EstimateId);
            return dbProjectEstimateItem;
        }

        public async Task<IList<DbProjectEstimateOption>> GetByProjectIdAsync(string projectIdOrShortCode, PagingInfo pagingInfo)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            IQueryable<DbProjectEstimateOption> estimatesQueryable = _dbContext.Entry(dbProject)
                .Collection(p => p.ProjectEstimateOptions)
                .Query();

            IQueryable<DbProjectEstimateItem> estimateItemsWithNullEstimateId = _dbContext.Entry(dbProject)
                .Collection(p => p.ProjectEstimateItems)
                .Query()
                .Where(item => string.IsNullOrEmpty(item.EstimateId));

            if (!estimatesQueryable.Any() && estimateItemsWithNullEstimateId.Any())
                await CreateAndSaveBackwardsCompatibleEstimateOptionAsync(dbProject.Id);

            IList<DbProjectEstimateOption> pagedEstimates = await estimatesQueryable.Include(a => a.ProjectEstimateItems)
                    .OrderBy(option => option.CreatedAt)
                    .AsPagedAsync(pagingInfo);

            IQueryable<DbProject> dbProjectQueryable = _dbContext.Projects.AsQueryable();
            IQueryable<DbProjectEstimateItem> dbProjectEstimateItemQueryable =
                _dbContext.ProjectEstimateItems.AsQueryable();

            foreach (DbProjectEstimateOption pagedEstimate in pagedEstimates)
                await _estimateTotalCalculator.SetProjectEstimateOptionTotalsAsync(
                    pagedEstimate, dbProjectQueryable, dbProjectEstimateItemQueryable);

            return pagedEstimates;
        }

        public async Task<DbProjectEstimateOption> CreateAndSaveBackwardsCompatibleEstimateOptionAsync(string projectId)
        {
            var newProjectEstimateOption = new DbProjectEstimateOption
            {
                Name = "Option 1",
                TemplateId = "" //Must set template id to empty string for cross platform (iOS) compatability
            };

            return await CreateAsync(projectId, newProjectEstimateOption);
        }

        public async Task<DbProjectEstimateOption> GetAsync(string projectIdOrShortCode, string estimateOptionId)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);
            if (dbProject == null)
                return null;

            DbProjectEstimateOption dbProjectEstimateOption = await _dbContext
                .Entry(dbProject)
                .Collection(p => p.ProjectEstimateOptions)
                .Query()

                .FirstOrDefaultAsync(option => option.Id == estimateOptionId);

            await _estimateTotalCalculator.SetProjectEstimateOptionTotalsAsync(
                dbProjectEstimateOption, _dbContext.Projects.AsQueryable(), _dbContext.ProjectEstimateItems.AsQueryable());

            return dbProjectEstimateOption;
        }

        public async Task<DbProjectEstimateOption> CreateAsync(string projectIdOrShortCode,
            DbProjectEstimateOption dbProjectEstimateOption)
        {
            // First we get the project we're going to create an estimate for.
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(projectIdOrShortCode);

            // Next we get a query for the estimates already associated with this project.
            // This query is reusable.
            IQueryable<DbProjectEstimateOption> projectEstimates = _dbContext.Entry(dbProject)
                .Collection(p => p.ProjectEstimateOptions)
                .Query();

            // If we don't have any estimates for this project yet, this one will be set as 'chosen'.
            // There should always be exactly one 'chosen' estimate.
            bool projectHasEstimates = projectEstimates.Any();
            if (!projectHasEstimates)
                dbProjectEstimateOption.IsChosen = true;

            DateTimeOffset now = _dateTimeOffsetWrapper.Now;
            dbProjectEstimateOption.UpdatedAt = now;

            // Associate the new estimate with this project
            dbProjectEstimateOption.ProjectId = dbProject.Id;

            // Get this project's drawing, if it has one. A project *shouldn't* have more than one project drawing.
            DbProjectDrawing drawing = dbProject.ProjectDrawings.FirstOrDefault();

            List<DbProjectEstimateItemInsertable> estimateItemsFromNullEstimateOrProjectDrawing = new List<DbProjectEstimateItemInsertable>();
            if (!projectHasEstimates)
            {
                estimateItemsFromNullEstimateOrProjectDrawing.AddRange(CreateEstimateItemsFromNullEstimate(dbProjectEstimateOption, dbProject));
            }
            else if (drawing != null)
            {
                estimateItemsFromNullEstimateOrProjectDrawing.AddRange(await CreateEstimateItemsFromProjectDrawing(dbProjectEstimateOption, drawing));
            }

            IList<DbProjectEstimateItemInsertable> itemsToAddFromTemplate =
                await CreateItemsToAddFromTemplate(dbProjectEstimateOption, estimateItemsFromNullEstimateOrProjectDrawing, dbProject);

            IList<DbProjectEstimateItemInsertable> allItemsToAdd = estimateItemsFromNullEstimateOrProjectDrawing.Concat(itemsToAddFromTemplate).ToArray();

            // Add the estimate and its items to the dbContext.
            _dbContext.ProjectEstimateOptions.Add(dbProjectEstimateOption);
            foreach (DbProjectEstimateItemInsertable item in allItemsToAdd)
                _dbContext.ProjectEstimateItemsInsertable.Add(item);

            // Persist changes so far to the database, making sure this didn't push the current esimate count > 5
            await _dbContext.SaveChangesAsync();

            //TODO: this is a limitation of how many estimates fit on a page in the documents, should be removed when documents is revisited
            //Scheming Orca looks for this exact string, do not change without updating client
            if (projectEstimates.Count() > 5)
                throw new EstimateOptionLimitExceededException("Maximum number of estimates already exist");


            // If we added items, recalculate the options totals.
            // This is being done in two units of work because DbProjectEstimateOptions don't get their properties updated when DbProjectEstimateOptionInsertables are saved.
            if (allItemsToAdd.Any())
            {
                await CalculateProjectEstimateOptionTotalsAsync(dbProjectEstimateOption.Id);
            }
            return dbProjectEstimateOption;
        }

        private async Task<IList<DbProjectEstimateItemInsertable>> CreateItemsToAddFromTemplate(DbProjectEstimateOption dbProjectEstimateOption,
            IList<DbProjectEstimateItemInsertable> itemsToAddFromProjectDrawing, DbProject dbProject)
        {
            DbEstimateTemplate estimateTemplate = null;
            if (!string.IsNullOrEmpty(dbProjectEstimateOption.TemplateId))
                estimateTemplate =
                    await _dbContext.EstimateTemplates
                        .Include(x => x.EstimateTemplateItems)
                        .FirstOrDefaultAsync(x => x.Id == dbProjectEstimateOption.TemplateId);

            if (!string.IsNullOrEmpty(dbProjectEstimateOption.TemplateId) && estimateTemplate == null)
                throw new EntityNotFoundException<DbEstimateTemplate>(dbProjectEstimateOption.TemplateId);

            return await GetItemsToAddFromTemplate(dbProjectEstimateOption, estimateTemplate, itemsToAddFromProjectDrawing, dbProject);
        }

        private async Task<IList<DbProjectEstimateItemInsertable>> GetItemsToAddFromTemplate(DbProjectEstimateOption dbProjectEstimateOption,
            DbEstimateTemplate estimateTemplate, IList<DbProjectEstimateItemInsertable> itemsToAddFromProjectDrawing, DbProject dbProject)
        {
            IList<DbProjectEstimateItemInsertable> templateItems = new List<DbProjectEstimateItemInsertable>();
            if (estimateTemplate == null) return templateItems;

            // Create estimate items from template
            // if an item is added to an estimate from the drawing, it should not be added from a template
            IEnumerable<DbProjectEstimateItemInsertable> dbProjectEstimateItemInsertables = estimateTemplate
                .EstimateTemplateItems
                .Where(eti =>
                    !itemsToAddFromProjectDrawing.Select(item => item.OfficePricedChargeableItemId)
                        .Contains(eti.ChargeableItemId))
                .Select(estimateTemplateItem =>
                    _dbProjectEstimateItemInsertableListFactory.Create(estimateTemplateItem));

            foreach (DbProjectEstimateItemInsertable dbProjectEstimateItemInsertable in
                dbProjectEstimateItemInsertables)
            {
                try
                {
                    templateItems.Add(await CreateEstimateItemInsertableAsync(dbProject, dbProjectEstimateOption,
                        dbProjectEstimateItemInsertable));
                }
                catch (MaterialItemNotFoundException e)
                {
                    _errorLogger.LogError(e);
                }

            }
            return templateItems;
        }

        private IList<DbProjectEstimateItemInsertable> CreateEstimateItemsFromNullEstimate(DbProjectEstimateOption dbProjectEstimateOption, DbProject dbProject)
        {
            // When creating the first non-null estimate, we copy anything from the existing 'null' estimate.
            // This will (should) include things from pins and facet attributes.

            IEnumerable<DbProjectEstimateItem> estimateItemsWithNullEstimate =
                _dbContext.Entry(dbProject)
                    .Collection(p => p.ProjectEstimateItems)
                    .Query()
                    .Where(items => string.IsNullOrEmpty(items.EstimateId));

            return estimateItemsWithNullEstimate.Select(item =>
                _projectEstimateItemInsertableFactory.Create(item, dbProjectEstimateOption.Id)).ToArray();
        }

        private async Task<IList<DbProjectEstimateItemInsertable>> CreateEstimateItemsFromProjectDrawing(DbProjectEstimateOption dbProjectEstimateOption,
            DbProjectDrawing drawing)
        {
            // Get OPCI queryable for the project's office id.
            long? officeId = drawing.Project.OfficeId; // TODO: Use the project id we have instead causing EF navigation
            IQueryable<DbOfficePricedChargeableItem> officePricedChargeableItemsQueryable = _dbContext
                .OfficePricedChargeableItems
                .Where(opci => opci.OfficeId == officeId)
                .Include(opci => opci.ChargeableItem);

            // Create new estimate items based off of the project drawing.
            IList<DbProjectEstimateItemInsertable> itemsToAdd =
                await _estimateItemsFactory.CreateFromProjectDrawingAsync(drawing,
                    officePricedChargeableItemsQueryable, dbProjectEstimateOption);

            return itemsToAdd;
        }

        public DbProjectEstimateOption Update(DbProjectEstimateOption updatedEstimateOption, byte[] version)
        {
            DateTimeOffset now = _dateTimeOffsetWrapper.Now;

            if (updatedEstimateOption.IsChosen == true)
            {
                IEnumerable<DbProjectEstimateOption> currentlySelectedEstimateOptions = updatedEstimateOption.Project.ProjectEstimateOptions
                    .Where(projectEstimateOption => projectEstimateOption.IsChosen == true)
                    .Except(new[] { updatedEstimateOption });

                foreach (DbProjectEstimateOption dbProjectEstimateOption in currentlySelectedEstimateOptions)
                {
                    dbProjectEstimateOption.UpdatedAt = now;
                    dbProjectEstimateOption.IsChosen = false;
                }
            }

            updatedEstimateOption.UpdatedAt = now;
            _dbContext.SetVersion(updatedEstimateOption, version);
            return updatedEstimateOption;
        }

        public async Task CalculateProjectEstimateOptionTotalsAsync(string projectEstimateOptionId)
        {
            DbProjectEstimateOption dbProjectEstimateOption =
                _dbContext.ProjectEstimateOptions.FirstOrDefault(o => o.Id == projectEstimateOptionId);

            await _estimateTotalCalculator.SetProjectEstimateOptionTotalsAsync(
                dbProjectEstimateOption,
                _dbContext.Projects.AsQueryable(),
                _dbContext.ProjectEstimateItems.AsQueryable());
        }

        private async Task SetEstimateItemRulesAsync(DbProject dbProject, params DbProjectEstimateItem[] estimateItems)
        {
            if (dbProject == null || estimateItems == null)
                return;

            DbProjectDrawing dbProjectDrawing = await _dbContext.Entry(dbProject)
                .Collection(project => project.ProjectDrawings)
                .Query()
                .FirstOrDefaultAsync();

            foreach (DbProjectEstimateItem estimateItem in estimateItems)
            {
                if (estimateItem != null)
                    estimateItem.Attributes = _projectEstimateItemRuleEvaluator.Evaluate(estimateItem, dbProjectDrawing);
            }
        }

        public async Task AppyIncidentalsAsync(string estimateId)
        {
            DbProjectEstimateOption dbProjectEstimateOption = _dbContext.ProjectEstimateOptions.FirstOrDefault(o => o.Id == estimateId);
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(dbProjectEstimateOption.ProjectId);
            long? officeId = dbProject?.OfficeId;

            var estimateRules = _roofsnapData.EstimateRules.Where(x => officeId == x.OfficeId).ToList();
            if (estimateRules != null)
            {
                var rule = estimateRules.Where(x => dbProjectEstimateOption.SubTotal >= Convert.ToDouble(x.MinValue) && (!x.MaxValue.HasValue || dbProjectEstimateOption.SubTotal <= Convert.ToDouble(x.MaxValue))).FirstOrDefault();
                if (rule != null && dbProjectEstimateOption.SubTotal.HasValue)
                {
                    var incidentals = estimateRules.Select(x => x.Name);

                    if (dbProjectEstimateOption.SubTotal > 0)
                    {
                        double estimateMarkUpTotal = (double)(dbProjectEstimateOption.SubTotal * (Convert.ToDouble(rule.ValueToApply) / 100));
                        dbProjectEstimateOption.GrandTotal = Math.Round(estimateMarkUpTotal + (double)dbProjectEstimateOption.SubTotal, 2);
                        dbProjectEstimateOption.Discount = dbProjectEstimateOption.GrandTotal;

                        bool incidentalAlreadyExists = CheckIfIncidentalExits(estimateId, incidentals, rule.Name);
                        if (!incidentalAlreadyExists)
                        {
                            //delete the old incidental and insert new one 
                            DeleteIncidentals(estimateId, incidentals);

                            DbProjectEstimateItemInsertable dbProjectEstimateItemInsertable = CreateIncidentals(dbProject.Id, estimateId, rule.Name, Convert.ToDouble(rule.ValueToApply));
                            _dbContext.ProjectEstimateItemsInsertable.Add(dbProjectEstimateItemInsertable);
                        }
                    }
                    else
                    {
                        DeleteIncidentals(estimateId, incidentals);
                    }
                    _dbContext.SaveChanges();
                }
            }
        }

        private bool CheckIfIncidentalExits(string estimateId, IEnumerable<string> incidentals, string newIncidental)
        {
            var incidental = _dbContext.ProjectEstimateItemsInsertable.Where(x => incidentals.Contains(x.Description) && x.EstimateId == estimateId).FirstOrDefault();
            if (incidental != null)
            {
                return incidental.Description == newIncidental;
            }
            return false;
        }

        private void DeleteIncidentals(string estimateId, IEnumerable<string> incidentals)
        {
            var incidentalsToDelete = _dbContext.ProjectEstimateItemsInsertable.Where(x => incidentals.Contains(x.Description) && x.EstimateId == estimateId);
            if (incidentalsToDelete != null)
            {
                ((DbSet<DbProjectEstimateItemInsertable>)_dbContext.ProjectEstimateItemsInsertable).RemoveRange(incidentalsToDelete);
            }

        }

        private DbProjectEstimateItemInsertable CreateIncidentals(string projectId, string estimateId, string description, double total)
        {
            return new DbProjectEstimateItemInsertable
            {
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
                ProjectId = projectId,
                UnitType = Areas.Estimating.DbUnitType.Each,
                ItemType = Areas.Estimating.DbItemType.Markup,
                Description = description,
                EstimateId = estimateId,
                Total = total,
                TotalPerUnit = total,
                AddedBySystem = true,
                Units = 1,
                CoveragePerUnit = 1
            };
        }

        public async Task UpdateSortOrder(List<EstimateSortOrder> estimateSortOrders)
        {
            var ids = estimateSortOrders.Select(e => e.Id).ToList();

            var estimateItems = await _dbContext.ProjectEstimateItemsInsertable
                .Where(item => ids.Contains(item.Id))
                .ToListAsync();

            foreach (var item in estimateItems)
            {
                var matchingEstimate = estimateSortOrders.FirstOrDefault(e => e.Id == item.Id);
                item.SortOrder = matchingEstimate.SortOrder;
            }
            await _dbContext.SaveChangesAsync();
        }
    }
}