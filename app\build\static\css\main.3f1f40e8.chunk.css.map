{"version": 3, "sources": ["Breadcrumb.css", "ProjectFilter.css", "ProjectList.css", "ProjectListItem.css", "ProjectHome.css", "ProjectNotes.css", "ProjectImages.css", "styles.css", "ProjectBoard.css"], "names": [], "mappings": "AAAA,6BAEI,QAAS,CACT,SACJ,CAEA,eACI,cAAe,CACf,eACJ,CAEA,qBACI,aAAc,CACd,oBAAqB,CACrB,eAAiB,CACjB,cACJ,CAEA,gCACI,UACJ,CAEA,mBAEI,yBAA0B,CAC1B,yBAA2B,CAC3B,eAAgB,CAChB,mBACJ,CAEA,uBACI,wBAA0B,CAC1B,gBACJ,CAEA,iCACI,eAAgB,CAChB,oBAAqB,CACrB,0BAA4B,CAC5B,2BAA6B,CAC7B,sBAAuB,CACvB,kBAAmB,CACnB,eAAgB,CAChB,qBACJ,CAEA,yBACI,iCACI,eACJ,CACJ,CClDA,QACI,YAAa,CACb,kBAAmB,CACnB,cAAe,CACf,oBACJ,CAEA,gBACI,iBAAkB,CAClB,mBACJ,CCVA,mBACI,UAAW,CACX,gBAAiB,CACjB,WACJ,CAEA,mBACI,YAAa,CACb,kBAAmB,CACnB,cAAe,CACf,0BAA4B,CAC5B,sBACJ,CCZA,mBACI,WAAY,CACZ,+BAAgC,CAChC,YAAa,CACb,gBACJ,CAEA,yBACI,wBAAyB,CACzB,cACJ,CAEA,8BACI,oBACJ,CAEA,iCACI,SAAU,CACV,eACJ,CAEA,uBACI,UACJ,CAEA,kCACI,kBAAmB,CACnB,gBAAiB,CACjB,SACJ,CAEA,oCACI,gBAAiB,CACjB,gBAAiB,CACjB,kBAAmB,CACnB,SACJ,CCpCA,sBACE,YAAa,CACb,cAAe,CACf,SAAU,CACV,aACF,CACA,+BACE,WAAY,CACZ,kBACF,CACA,6BACE,YAAa,CACb,cAAe,CACf,UAAW,CACX,iBACF,CACA,iBACE,SAAU,CACV,iBAAkB,CAClB,cACF,CACA,gCACE,gBACF,CACA,qBACE,oBAAyB,CACzB,qBAAuB,CACvB,oBACF,CACA,+DAOE,sEACE,UACF,CACA,gCACE,cACF,CACF,CACA,gEACE,sBACE,UACF,CACF,CC/CA,uBACI,YAAa,CACb,cAAe,CACf,SAAU,CACV,aACJ,CACA,QACI,aACJ,CACA,6EAGI,uBACI,UACJ,CACJ,CCfA,UACI,YAAa,CACb,cAAe,CACf,sBACJ,CACA,UACE,cAAe,CACf,sBAAwB,CACxB,qBAAsB,CACtB,UAAW,CACX,WAAY,CACZ,sBACF,CACA,eACE,WAAY,CACZ,UAAW,CACX,gBAAiB,CACjB,cACF,CACA,gBACE,cACF,CACA,uCACE,+BAAiC,CACjC,cAAe,CACf,qBAAuB,CACvB,oBACF,CAEA,+DACE,uCACE,qBAAuB,CACvB,oBACF,CACA,UACE,sBAAwB,CACxB,qBACF,CACF,CClCA,UAHI,WAOJ,CAJA,KACI,QAAS,CAET,wBACJ,CAGA,eACI,YACJ,CAEA,MACI,WACJ,CAEA,4BACI,0BAA4B,CAC5B,yBACJ,CCtBA,mBACE,eACF,CACA,mBACE,WACF,CACA,kBACE,yBACF", "file": "main.3f1f40e8.chunk.css", "sourcesContent": ["ul.breadcrumb, .breadcrumb li\r\n{\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.breadcrumb li {\r\n    display: inline;\r\n    list-style: none;\r\n}\r\n\r\n.breadcrumb li::after {\r\n    content: \" > \";\r\n    color: rgba(0,0,0,.5);\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n}\r\n\r\n.breadcrumb li:last-child::after {\r\n    content: \"\";\r\n}\r\n\r\n.breadcrumb button {\r\n    margin: 0 !important;\r\n    margin-left: 40 !important;\r\n    min-width: unset !important;\r\n    max-width: 300px;\r\n    padding: 0 !important;\r\n}\r\n\r\n.breadcrumb button svg {\r\n    margin-right: 0 !important;\r\n    margin-left: -5px;\r\n}\r\n\r\n.breadcrumb button span div span {\r\n    max-width: 300px;\r\n    display: inline-block;\r\n    padding-left: 5px !important;\r\n    padding-right: 5px !important;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    vertical-align: middle;\r\n}\r\n\r\n@media (max-width: 720px) {\r\n    .breadcrumb button span div span {\r\n        max-width: 200px;\r\n    }\r\n}\r\n", ".filter {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex-wrap: wrap;\r\n    align-items: flex-end;\r\n}\r\n\r\n.filter .select {\r\n    margin-right: 30px;\r\n    width: 25% !important;\r\n}\r\n", ".project-list-card {\r\n    width: 100%;\r\n    max-width: 1350px;\r\n    margin: auto;\r\n}\r\n\r\n.project-list-body {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex-wrap: wrap;\r\n    padding-top: 20px !important;\r\n    justify-content: center;\r\n}\r\n\r\n", ".project-list-item {\r\n    padding: 8px;\r\n    border-bottom: solid 1px #dbdbdb;\r\n    display: flex;\r\n    min-height: 100px;\r\n}\r\n\r\n.project-list-item:hover {\r\n    background-color: #e8e7e7;\r\n    cursor: pointer;\r\n}\r\n\r\n.project-list-item > .container {\r\n    display: inline-block;\r\n}\r\n\r\n.project-list-item-img-container {\r\n    width: 10%;\r\n    min-width: 150px;\r\n}\r\n\r\n.project-list-item-img {\r\n    width: 100%;\r\n}\r\n\r\n.project-list-item-main-container {\r\n    vertical-align: top;\r\n    padding: 16px 8px;\r\n    width: 80%;\r\n}\r\n\r\n.project-list-item-status-container {\r\n    text-align: right;\r\n    padding-top: 16px;\r\n    vertical-align: top;\r\n    width: 10%;\r\n}", ".projectHomeContainer {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  width: 60%;\r\n  margin: 0 auto;\r\n}\r\n.projectHomeContainer .details {\r\n  width: 98.7%;\r\n  margin-bottom: 10px;\r\n}\r\n.projectHomeContainer .cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n.projectHomeCard {\r\n  width: 49%;\r\n  margin-bottom: 5px;\r\n  cursor: pointer;\r\n}\r\n.projectHomeCard:nth-child(odd) {\r\n  margin-right: 6px;\r\n}\r\n.projectHomeCard svg {\r\n  color: #ffffff !important;\r\n  height: 70px !important;\r\n  width: 70px !important;\r\n}\r\n@media only screen and (min-width: 320px) and (max-width: 480px) {\r\n  .projectHomeContainer .details {\r\n    width: 100%;\r\n  }\r\n  .projectHomeContainer {\r\n    width: 100%;\r\n  }\r\n  .projectHomeCard {\r\n    width: 100%;\r\n  }\r\n  .projectHomeCard:nth-child(odd) {\r\n    margin-right: 0px;\r\n  }\r\n}\r\n@media only screen and (min-width: 480px) and (max-width: 1024px) {\r\n  .projectHomeContainer {\r\n    width: 100%;\r\n  }\r\n}\r\n", ".projectNotesContainer {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    width: 75%;\r\n    margin: 0 auto;\r\n}\r\n.loader {\r\n    margin: 0 auto;\r\n}\r\n@media only screen\r\nand (min-device-width: 320px)\r\nand (max-device-width: 480px) {\r\n    .projectNotesContainer {\r\n        width: 100%;\r\n    }\r\n}\r\n", ".gridList {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n}\r\n.gridTile {\r\n  cursor: pointer;\r\n  outline: 1px black solid;\r\n  box-sizing: border-box;\r\n  margin: 5px;\r\n  width: 200px;\r\n  height: 200px !important;\r\n}\r\n.gridTileImage {\r\n  height: 100%;\r\n  width: 100%;\r\n  object-fit: cover;\r\n  cursor: pointer;\r\n}\r\n.cursor-pointer {\r\n  cursor: pointer;\r\n}\r\n.keyBoardArrowLeft, .keyBoardArrowRight {\r\n  color: rgba(0,0,0,.53) !important;\r\n  cursor: pointer;\r\n  height: 75px !important;\r\n  width: 75px !important;\r\n}\r\n\r\n@media only screen and (min-width: 320px) and (max-width: 480px) {\r\n  .keyBoardArrowLeft, .keyBoardArrowRight {\r\n    height: 40px !important;\r\n    width: 40px !important;\r\n  }\r\n  .gridTile {\r\n    height: 100px !important;\r\n    width: 100px !important;\r\n  }\r\n}\r\n", "html {\r\n    height: 100%;\r\n}\r\n\r\nbody {\r\n    margin: 0;\r\n    height: 100%;\r\n    background-color: #F2F2F2;\r\n}\r\n\r\n/* Prevents Google Map search box from hiding behind dialogs */\r\n.pac-container {\r\n    z-index: 9000;\r\n}\r\n\r\n#root {\r\n    height: 100%;\r\n}\r\n\r\n[data-braintree-id=\"methods\"] {\r\n    max-height: 300px !important;\r\n    overflow-y: auto !important;\r\n}", ".react-trello-card {\r\n  max-width: 320px;\r\n}\r\n.react-trello-lane {\r\n  width: 325px;\r\n}\r\n.card-title:hover {\r\n  text-decoration: underline;\r\n}\r\n"]}