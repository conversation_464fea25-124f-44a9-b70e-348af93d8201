﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using RoofSnap.WebAPI.Areas.Projects;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.WebAPI;
using RoofSnap.WebAPI.Common.Wrappers;

namespace RoofSnap.WebAPI.Areas.Offices.Estimating.Templates
{
    public interface IEstimateTemplateService : IService<DbEstimateTemplate, string>
    {
        Task<DbEstimateTemplate> CreateEstimateTemplateAsync(string id, string estimateId, DbEstimateTemplate dbEstimateTemplate);
        void DeleteEstimateTemplate(DbEstimateTemplate dbEstimateTemplate);
    }

    public class EstimateTemplateService : IEstimateTemplateService
    {
        private readonly IDateTimeWrapper _dateTimeWrapper;
        private readonly IDbEstimateTemplateItemFactory _dbEstimateTemplateItemFactory;
        private readonly IRoofSnapLiveDbContext _dbContext;

        public EstimateTemplateService(IDateTimeWrapper dateTimeWrapper, 
            IDbEstimateTemplateItemFactory dbEstimateTemplateItemFactory,
            IRoofSnapLiveDbContext dbContext)
        {
            _dateTimeWrapper = dateTimeWrapper;
            _dbEstimateTemplateItemFactory = dbEstimateTemplateItemFactory;
            _dbContext = dbContext;
        }

        public Task<IList<DbEstimateTemplate>> GetPagedAsync(PagingInfo pagingInfo, string search = "", string[] ids = null)
        {
            return _dbContext.EstimateTemplates
                .OrderByDescending(estimatTemplate => estimatTemplate.CreatedAt)
                .AsPagedAsync(pagingInfo);
        }

        public DbEstimateTemplate Get(string id)
        {
            return _dbContext.EstimateTemplates.Find(id);
        }

        public void DeleteEstimateTemplate(DbEstimateTemplate dbEstimateTemplate)
        {
            _dbContext.EstimateTemplates.Remove(dbEstimateTemplate);
        }

        public async Task<DbEstimateTemplate> CreateEstimateTemplateAsync(string id, string estimateId, DbEstimateTemplate dbEstimateTemplate)
        {
            DbProject dbProject = await _dbContext.Projects.GetByIdOrShortCodeAsync(id);
            if (dbProject == null)
                return null;

            dbEstimateTemplate.OrganizationId = dbProject.OrganizationId;
            DateTime now = _dateTimeWrapper.Now();
            dbEstimateTemplate.UpdatedAt = now;
            //this nonsense is required for iOS compatability
            {
                dbEstimateTemplate.OfficeId = null;
                dbEstimateTemplate.IsActive = null;
                dbEstimateTemplate.CreatedBy = "";
            }

            _dbContext.EstimateTemplates.Add(dbEstimateTemplate);


            DbProjectEstimateOption estimate = await _dbContext.Entry(dbProject)
                .Collection(project => project.ProjectEstimateOptions)
                .Query()
                .FirstOrDefaultAsync(projectEstimateOption => projectEstimateOption.Id == estimateId);

            if (estimate == null)
                throw new EntityNotFoundException<DbProjectEstimateOption>(estimateId);

            if (estimate.ProjectEstimateItems.Any())
            {
                //if an office priced chargeable item has a chargeable item id of 0, it is a custom item and we do not want that in the template
                IEnumerable<DbEstimateTemplateItem> estimateTemplateItems = estimate.ProjectEstimateItems
                    .Where(ei => ei.OfficePricedChargeableItem != null && ei.OfficePricedChargeableItem.ChargeableItemId != 0)
                    .Select(estimateItem => _dbEstimateTemplateItemFactory.Create(dbEstimateTemplate.Id, estimateItem));

                foreach (DbEstimateTemplateItem dbEstimateTemplateItem in estimateTemplateItems)
                {
                    _dbContext.EstimateTemplateItems.Add(dbEstimateTemplateItem);
                }
            }

            return dbEstimateTemplate;
        }
    }
}