﻿CREATE VIEW [roofsnaplive].[FixedProjectEstimateItems] WITH SCHEMABINDING
	AS SELECT pei.id,
pei.__createdAt,
pei.__updatedAt,
pei.__version,
pei.projectid,
pei.unittype,
pei.itemtype,
pei.[description],
pei.[image],
pei.units,
pei.coverageperunit,
pei.total AS Total,
pei.systemchargeableitemid,
pei.colorid,
cic.id AS ChargeableItemColorId,
opcico.id AS OfficePricedChargeableItemColorOptionId,
CAST (pei.officepricedchargeableitemid AS BIGINT) AS OfficePricedChargeableItemId,
TRY_CAST(pei.estimatecategoryid AS BIGINT) AS CategoryId,
CAST (CASE WHEN TRY_CAST(pei.estimatecategoryid AS bigint) IS NULL THEN pei.estimatecategoryid ELSE NULL END AS nvarchar(255)) AS OfficeCustomCategoryId,
pei.estimateid,
pei.labor AS Labor,
pei.material AS Material,
pei.materialorderdescription,
pei.hideonestimate,
pei.hideoncontract,
pei.hideonmaterialorder,
pei.HideOnLaborReport,
pei.totalperunit,
pei.addedBySystem,
pei.SortOrder
FROM roofsnaplive.ProjectEstimateItems pei
	LEFT JOIN roofsnaplive.ChargeableItemColors cic on cic.id = pei.colorid
	LEFT JOIN roofsnaplive.OfficePricedChargeableItemColorOptions opcico on opcico.id = pei.colorid