﻿CREATE TABLE [roofsnaplive].[ProjectEstimateItems] (
    [Id]                           NVARCHAR (255)     CONSTRAINT [def_ProjectEstimateItems_id] DEFAULT (CONVERT([nvarchar](255),newid(),(0))) NOT NULL,
    [__createdAt]                  DATETIMEOFFSET (3) CONSTRAINT [DF_ProjectEstimateItems___createdAt] DEFAULT (CONVERT([datetimeoffset](3),sysutcdatetime(),(0))) NOT NULL,
    [__updatedAt]                  DATETIMEOFFSET (3) CONSTRAINT [DF_ProjectEstimateItems___updatedAt] DEFAULT SYSDATETIMEOFFSET() NULL,
    [__version]                    ROWVERSION         NOT NULL,
    [projectid]                    NVARCHAR (255)     NULL,
    [categoryid]                   FLOAT (53)         NULL,
    [unittype]                     INT                NULL,
    [itemtype]                     INT                NULL,
    [description]                  NVARCHAR (MAX)     NULL,
    [image]                        NVARCHAR (MAX)     NULL,
    [units]                        FLOAT (53)         NOT NULL DEFAULT ((0)),
    [coverageperunit]              FLOAT (53)         NULL,
    [total]                        FLOAT (53)         NULL,
    [order]                        FLOAT (53)         NULL,
    [systemchargeableitemid]       BIGINT             NOT NULL,
    [colorid]                      NVARCHAR (255)     NULL,
    [officepricedchargeableitemid] BIGINT             NULL,
    [estimatecategoryid]           NVARCHAR (255)     NULL,
    [estimateid]                   NVARCHAR (255)     NULL,
    [labor]                        FLOAT (53)         NULL,
    [material]                     FLOAT (53)         NULL,
    [materialorderdescription]     NVARCHAR (MAX)     NULL,
    [hideonestimate]               BIT                NULL,
    [hideoncontract]               BIT                NULL,
    [hideonmaterialorder]          BIT                NULL,
    [totalperunit]                 FLOAT (53)         NULL,
    [sortOrder]                    INT                NULL, 
    [addedBySystem] BIT NOT NULL DEFAULT ((0)), 
    [HideOnLaborReport] BIT NOT NULL DEFAULT ((0)), 
    CONSTRAINT [PK_ProjectEstimateItems] PRIMARY KEY NONCLUSTERED ([id] ASC)
);


GO
CREATE CLUSTERED INDEX [__createdAt]
    ON [roofsnaplive].[ProjectEstimateItems]([__createdAt] ASC);


GO
CREATE NONCLUSTERED INDEX [nci_wi_ProjectEstimateItems_7FB01E2C0EE2B4914CEB1A48463801DC]
    ON [roofsnaplive].[ProjectEstimateItems]([officepricedchargeableitemid] ASC, [systemchargeableitemid] ASC, [labor] ASC)
    INCLUDE([description], [estimateid], [projectid], [units], [unittype]);


GO
CREATE NONCLUSTERED INDEX [IX_ProjectEstimateItems_projectid]
    ON [roofsnaplive].[ProjectEstimateItems]([projectid] ASC);


GO
CREATE NONCLUSTERED INDEX [nci_wi_ProjectEstimateItems_A0405BF48CBB233484CC50B628A2333C]
    ON [roofsnaplive].[ProjectEstimateItems]([estimateid] ASC)
    INCLUDE([__updatedAt], [__version], [colorid], [coverageperunit], [description], [estimatecategoryid], [hideoncontract], [hideonestimate], [hideonmaterialorder], [id], [image], [itemtype], [labor], [material], [materialorderdescription], [officepricedchargeableitemid], [projectid], [systemchargeableitemid], [total], [totalperunit], [units], [unittype], [HideOnLaborReport]);

GO
CREATE NONCLUSTERED INDEX [nci_wi_ProjectEstimateItems_7450EB2494C204B45D5FC3F5B9424153] ON [roofsnaplive].[ProjectEstimateItems] ([id], [__version])
INCLUDE ([__updatedAt], [colorid], [coverageperunit], [description], [estimatecategoryid], [estimateid], [hideoncontract], [hideonestimate], [hideonmaterialorder], [image], [itemtype], [labor], [material], [materialorderdescription], [officepricedchargeableitemid], [order], [projectid], [systemchargeableitemid], [total], [totalperunit], [units], [unittype], [HideOnLaborReport])

GO
CREATE NONCLUSTERED INDEX [nci_wi_ProjectEstimateItems_50BC6D6B5CE56EDCD631E1A58A689BFA] ON [roofsnaplive].[ProjectEstimateItems] ([projectid], [unittype], [itemtype]) 
INCLUDE ([__updatedAt], [__version], [categoryid], [colorid], [coverageperunit], [description], [estimatecategoryid], [estimateid], [hideoncontract], [hideonestimate], [hideonmaterialorder], [id], [image], [labor], [material], [materialorderdescription], [officepricedchargeableitemid], [order], [systemchargeableitemid], [total], [totalperunit], [units], [HideOnLaborReport])

GO
CREATE NONCLUSTERED INDEX [nci_wi_ProjectEstimateItems_E81FFDF573D1CB1230335253D4EE9C0C] ON [roofsnaplive].[ProjectEstimateItems] ([projectid]) 
INCLUDE ([__updatedAt], [__version], [addedBySystem], [categoryid], [colorid], [coverageperunit], [description], [estimatecategoryid], [estimateid], [hideoncontract], [hideonestimate], [hideonmaterialorder], [id], [image], [itemtype], [labor], [material], [materialorderdescription], [officepricedchargeableitemid], [order], [systemchargeableitemid], [total], [totalperunit], [units], [unittype], [HideOnLaborReport])

GO
CREATE TRIGGER roofsnaplive.TR_ProjectEstimateItems_InsertUpdate ON roofsnaplive.ProjectEstimateItems
AFTER INSERT, UPDATE
AS BEGIN
SET NOCOUNT ON
IF TRIGGER_NESTLEVEL(OBJECT_ID('roofsnaplive.TR_ProjectEstimateItems_InsertUpdate')) > 1 RETURN
IF UPDATE(__updatedAt) RETURN
UPDATE roofsnaplive.ProjectEstimateItems SET __updatedAt = CONVERT (DATETIMEOFFSET(3), SYSUTCDATETIME())
FROM roofsnaplive.ProjectEstimateItems t INNER JOIN INSERTED i on t.Id = i.id
END